#!/usr/bin/env python3
"""
Test script to debug NudeNet detection issues
"""

import sys
import os
from nudenet import NudeDetector
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_nudenet_basic():
    """Test basic NudeNet functionality"""
    print("=== Testing NudeNet Basic Functionality ===")
    
    try:
        # Initialize detector
        print("1. Loading NudeNet detector...")
        detector = NudeDetector()
        print(f"   ✓ Detector loaded: {type(detector)}")
        
        # Check available methods
        print("2. Checking available methods...")
        methods = ['detect', 'detect_video', 'censor', 'predict', 'classes']
        for method in methods:
            has_method = hasattr(detector, method)
            print(f"   {method}: {'✓' if has_method else '✗'}")
            if has_method and method == 'classes':
                try:
                    classes = getattr(detector, method)
                    print(f"      Classes: {classes}")
                except:
                    print(f"      Classes: Could not access")
        
        # Test with a simple test image (create a small colored image)
        print("3. Creating test image...")
        test_image = Image.new('RGB', (100, 100), color='red')
        img_buffer = io.BytesIO()
        test_image.save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        
        print("4. Testing detection methods...")
        
        # Test detect method
        try:
            results = detector.detect(img_buffer.getvalue())
            print(f"   detect() result: {results}")
            print(f"   Number of detections: {len(results) if results else 0}")
        except Exception as e:
            print(f"   detect() error: {e}")
        
        # Test censor method if available
        if hasattr(detector, 'censor'):
            try:
                img_buffer.seek(0)
                censor_result = detector.censor(img_buffer.getvalue())
                print(f"   censor() result type: {type(censor_result)}")
                print(f"   censor() result: {str(censor_result)[:100]}...")
            except Exception as e:
                print(f"   censor() error: {e}")
        
        # Test predict method if available
        if hasattr(detector, 'predict'):
            try:
                img_buffer.seek(0)
                predict_result = detector.predict(img_buffer.getvalue())
                print(f"   predict() result: {predict_result}")
            except Exception as e:
                print(f"   predict() error: {e}")
        
        print("5. Testing with different image formats...")
        
        # Test with different image modes
        for mode in ['RGB', 'RGBA', 'L']:
            try:
                test_img = Image.new(mode, (200, 200), color='blue')
                if mode != 'RGB':
                    test_img = test_img.convert('RGB')
                
                buf = io.BytesIO()
                test_img.save(buf, format='JPEG')
                buf.seek(0)
                
                results = detector.detect(buf.getvalue())
                print(f"   {mode} -> RGB: {len(results) if results else 0} detections")
            except Exception as e:
                print(f"   {mode} test error: {e}")
        
        print("\n=== Test Complete ===")
        return detector
        
    except Exception as e:
        print(f"ERROR: {e}")
        return None

def test_with_actual_image(detector, image_path):
    """Test with an actual image file if provided"""
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return
    
    print(f"\n=== Testing with actual image: {image_path} ===")
    
    try:
        # Load image
        image = Image.open(image_path)
        print(f"Image size: {image.size}, mode: {image.mode}")
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Save to bytes
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG', quality=95)
        img_buffer.seek(0)
        
        # Test detection
        results = detector.detect(img_buffer.getvalue())
        print(f"Detection results: {results}")
        print(f"Number of detections: {len(results) if results else 0}")
        
        if results:
            for i, detection in enumerate(results):
                print(f"  Detection {i+1}: {detection}")
        
    except Exception as e:
        print(f"Error testing with image: {e}")

if __name__ == "__main__":
    detector = test_nudenet_basic()
    
    # If an image path is provided as argument, test with it
    if len(sys.argv) > 1 and detector:
        test_with_actual_image(detector, sys.argv[1])
    
    print("\nTo test with a specific image, run:")
    print("python test_nudenet.py /path/to/your/image.jpg")
