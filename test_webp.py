#!/usr/bin/env python3
"""
Specific test for WebP images with NudeNet
"""

import sys
import os
from nudenet import NudeDetector
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_webp_image(image_path):
    """Test WebP image with enhanced processing"""
    print(f"=== Testing WebP Image: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return
    
    try:
        # Initialize detector
        detector = NudeDetector()
        
        # Load image
        image = Image.open(image_path)
        print(f"Original: size={image.size}, mode={image.mode}, format={image.format}")
        
        # Check if animated
        if hasattr(image, 'is_animated') and image.is_animated:
            print(f"Animated image with {image.n_frames} frames")
            
            # Test multiple frames
            for frame_num in range(min(3, image.n_frames)):  # Test first 3 frames
                print(f"\n--- Testing Frame {frame_num} ---")
                image.seek(frame_num)
                frame = image.copy()
                
                # Process frame
                if frame.mode != 'RGB':
                    if frame.mode in ('RGBA', 'LA'):
                        background = Image.new('RGB', frame.size, (255, 255, 255))
                        if frame.mode == 'RGBA':
                            background.paste(frame, mask=frame.split()[-1])
                        else:
                            background.paste(frame)
                        frame = background
                    else:
                        frame = frame.convert('RGB')
                
                # Test detection on this frame
                test_frame_detection(detector, frame, f"frame_{frame_num}")
        else:
            print("Static image")
            # Process static image
            if image.mode != 'RGB':
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'RGBA':
                        background.paste(image, mask=image.split()[-1])
                    else:
                        background.paste(image)
                    image = background
                else:
                    image = image.convert('RGB')
            
            test_frame_detection(detector, image, "static")
            
    except Exception as e:
        print(f"Error: {e}")

def test_frame_detection(detector, image, frame_name):
    """Test detection on a single frame/image"""
    try:
        print(f"  {frame_name}: size={image.size}, mode={image.mode}")
        
        # Test different sizes
        sizes_to_test = [
            ("original", image.size),
            ("large", (1024, 1024)),
            ("medium", (512, 512)),
            ("small", (256, 256))
        ]
        
        best_results = []
        best_count = 0
        
        for size_name, target_size in sizes_to_test:
            test_img = image.copy()
            if test_img.size != target_size:
                test_img.thumbnail(target_size, Image.Resampling.LANCZOS)
            
            # Test different quality settings
            for quality in [95, 85, 75]:
                try:
                    # Save as JPEG bytes
                    buffer = io.BytesIO()
                    test_img.save(buffer, format='JPEG', quality=quality)
                    buffer.seek(0)
                    
                    # Test detection
                    results = detector.detect(buffer.getvalue())
                    count = len(results) if results else 0
                    
                    print(f"    {size_name} (q{quality}): {count} detections")
                    if results:
                        for i, det in enumerate(results):
                            print(f"      {i+1}: {det}")
                    
                    if count > best_count:
                        best_results = results
                        best_count = count
                        
                except Exception as e:
                    print(f"    {size_name} (q{quality}): Error - {e}")
        
        print(f"  Best result for {frame_name}: {best_count} detections")
        return best_results
        
    except Exception as e:
        print(f"  Error testing {frame_name}: {e}")
        return []

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_webp_image(sys.argv[1])
    else:
        print("Usage: python test_webp.py <webp_image_path>")
        print("Example: python test_webp.py test_images/23793667.webp")
