from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
import async<PERSON>
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from nudenet import NudeDetector
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="NudeNet Detection API", version="1.0.0")

# Global detector instance (loaded once)
detector = None
executor = ThreadPoolExecutor(max_workers=2)  # Limit concurrent processing

@app.on_event("startup")
async def startup_event():
    global detector
    logger.info("Loading NudeNet model...")
    detector = NudeDetector()
    logger.info("Model loaded successfully")

def process_image(image_bytes: bytes) -> dict:
    """CPU-optimized image processing"""
    try:
        # Open and optimize image
        image = Image.open(io.BytesIO(image_bytes))
        
        # Resize large images to reduce CPU load
        max_size = 1024
        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Save to bytes for NudeNet
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG', quality=85, optimize=True)
        img_buffer.seek(0)
        
        # Run detection
        results = detector.detect(img_buffer.getvalue())
        
        return {
            "status": "success",
            "detections": results,
            "image_size": image.size
        }
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/detect")
async def detect_nudity(file: UploadFile = File(...)):
    """Detect nudity in uploaded image"""
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Read file
    contents = await file.read()
    
    # Process in thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, process_image, contents)
    
    return JSONResponse(content=result)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "model_loaded": detector is not None}

@app.get("/")
async def root():
    return {"message": "NudeNet Detection API", "docs": "/docs"}