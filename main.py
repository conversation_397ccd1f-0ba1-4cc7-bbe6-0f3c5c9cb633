from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import <PERSON><PERSON>NResponse
from contextlib import asynccontextmanager
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor
from nudenet import NudeDetector
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global detector instance (loaded once)
detector = None
executor = ThreadPoolExecutor(max_workers=2)  # Limit concurrent processing

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global detector
    logger.info("Loading NudeNet model...")
    # Initialize with explicit model path and lower threshold for better detection
    detector = NudeDetector()
    logger.info("Model loaded successfully")
    logger.info(f"Model type: {type(detector)}")
    yield
    # Shutdown (cleanup if needed)
    pass

app = FastAPI(title="NudeNet Detection API", version="1.0.0", lifespan=lifespan)
def process_image(image_bytes: bytes) -> dict:
    """CPU-optimized image processing"""
    try:
        # Open and optimize image
        image = Image.open(io.BytesIO(image_bytes))
        logger.info(f"Original image size: {image.size}, mode: {image.mode}")

        # Resize large images to reduce CPU load
        max_size = 1024
        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            logger.info(f"Resized image to: {image.size}")

        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
            logger.info("Converted image to RGB")

        # Save to bytes for NudeNet
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='JPEG', quality=85, optimize=True)
        img_buffer.seek(0)

        # Run detection with debugging
        logger.info("Running NudeNet detection...")
        results = detector.detect(img_buffer.getvalue())
        logger.info(f"Detection results: {results}")
        logger.info(f"Number of detections: {len(results) if results else 0}")

        # Also try with different method if available
        try:
            # Some versions of nudenet have different methods
            if hasattr(detector, 'detect_video'):
                logger.info("Detector has detect_video method")
            if hasattr(detector, 'censor'):
                logger.info("Detector has censor method")
        except Exception as method_check_error:
            logger.info(f"Method check error: {method_check_error}")

        return {
            "status": "success",
            "detections": results,
            "image_size": image.size,
            "detection_count": len(results) if results else 0
        }
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/detect")
async def detect_nudity(file: UploadFile = File(...)):
    """Detect nudity in uploaded image"""
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Read file
    contents = await file.read()
    
    # Process in thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, process_image, contents)
    
    return JSONResponse(content=result)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "model_loaded": detector is not None}

@app.get("/")
async def root():
    return {"message": "NudeNet Detection API", "docs": "/docs"}