from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import async<PERSON>
from concurrent.futures import Thread<PERSON>oolExecutor
from nudenet import NudeDetector
from PIL import Image
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global detector instance (loaded once)
detector = None
executor = ThreadPoolExecutor(max_workers=2)  # Limit concurrent processing

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global detector
    logger.info("Loading NudeNet model...")
    # Initialize with explicit model path and lower threshold for better detection
    # Try different model types if available
    try:
        # Try with default model first - some versions support model_path parameter
        try:
            # Try to load with 'base' model which is more sensitive
            detector = NudeDetector(model_path='base')
            logger.info("Model loaded successfully with base model")
        except TypeError:
            # If model_path parameter not supported, use default
            detector = NudeDetector()
            logger.info("Model loaded successfully with default settings")
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        # Fallback to basic initialization
        detector = NudeDetector()
        logger.info("Model loaded with fallback method")

    logger.info(f"Model type: {type(detector)}")

    # Check if detector has any configuration options
    try:
        if hasattr(detector, 'model'):
            logger.info(f"Detector model info: {detector.model}")
        if hasattr(detector, 'classes'):
            logger.info(f"Detector classes: {detector.classes}")
    except Exception as e:
        logger.info(f"Could not get model details: {e}")

    yield
    # Shutdown (cleanup if needed)
    pass

app = FastAPI(title="NudeNet Detection API", version="1.0.0", lifespan=lifespan)
def process_image(image_bytes: bytes) -> dict:
    """CPU-optimized image processing"""
    try:
        # Open and optimize image
        image = Image.open(io.BytesIO(image_bytes))
        logger.info(f"Original image size: {image.size}, mode: {image.mode}, format: {image.format}")

        # Handle animated images (GIF, WebP) - extract first frame
        if hasattr(image, 'is_animated') and image.is_animated:
            logger.info("Detected animated image, extracting first frame")
            image.seek(0)  # Go to first frame
            image = image.copy()  # Make a copy to avoid issues

        # Handle WebP specifically
        if image.format == 'WEBP':
            logger.info("Processing WebP image")
            # For WebP, sometimes we need to force RGB conversion
            if image.mode in ('RGBA', 'LA'):
                # Create white background for transparency
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])  # Use alpha channel as mask
                else:
                    background.paste(image)
                image = background
                logger.info("WebP: Converted RGBA to RGB with white background")

        # Resize large images to reduce CPU load, but keep reasonable size for detection
        max_size = 1024
        min_size = 256  # Don't make it too small

        if max(image.size) > max_size:
            image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            logger.info(f"Resized large image to: {image.size}")
        elif max(image.size) < min_size:
            # Upscale very small images
            scale_factor = min_size / max(image.size)
            new_size = (int(image.size[0] * scale_factor), int(image.size[1] * scale_factor))
            image = image.resize(new_size, Image.Resampling.LANCZOS)
            logger.info(f"Upscaled small image to: {image.size}")

        # Convert to RGB if needed
        if image.mode != 'RGB':
            if image.mode in ('RGBA', 'LA'):
                # Create white background for transparency
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image)
                image = background
                logger.info(f"Converted {image.mode} to RGB with white background")
            else:
                image = image.convert('RGB')
                logger.info(f"Converted {image.mode} to RGB")

        # Save to bytes for NudeNet with multiple quality settings
        img_buffers = {}

        # Try different JPEG quality settings for better detection
        quality_settings = [95, 85, 75]  # High to medium quality

        for quality in quality_settings:
            buffer = io.BytesIO()
            image.save(buffer, format='JPEG', quality=quality, optimize=True)
            buffer.seek(0)
            img_buffers[f'quality_{quality}'] = buffer

        # Run detection with debugging
        logger.info("Running NudeNet detection...")

        # Try with different quality settings
        all_results = {}
        best_results = []
        best_count = 0

        for quality_name, img_buffer in img_buffers.items():
            try:
                # Method 1: Try with bytes
                img_buffer.seek(0)
                results = detector.detect(img_buffer.getvalue())
                logger.info(f"Detection results ({quality_name}): {results}")
                logger.info(f"Number of detections ({quality_name}): {len(results) if results else 0}")

                all_results[quality_name] = {
                    "method": "bytes",
                    "results": results,
                    "count": len(results) if results else 0
                }

                # Keep track of best results
                if results and len(results) > best_count:
                    best_results = results
                    best_count = len(results)

                # Method 2: Try saving to temporary file (some versions work better with file paths)
                import tempfile
                import os
                try:
                    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as temp_file:
                        img_buffer.seek(0)
                        temp_file.write(img_buffer.getvalue())
                        temp_file.flush()
                        temp_results = detector.detect(temp_file.name)
                        logger.info(f"Detection results ({quality_name} file): {temp_results}")
                        logger.info(f"Number of detections ({quality_name} file): {len(temp_results) if temp_results else 0}")

                        # Clean up temp file
                        os.unlink(temp_file.name)

                        # Store file results
                        all_results[f"{quality_name}_file"] = {
                            "method": "file",
                            "results": temp_results,
                            "count": len(temp_results) if temp_results else 0
                        }

                        # Use file results if they're better
                        if temp_results and len(temp_results) > best_count:
                            best_results = temp_results
                            best_count = len(temp_results)
                            logger.info(f"Using file-based detection results from {quality_name}")

                except Exception as temp_error:
                    logger.info(f"Temp file detection error ({quality_name}): {temp_error}")

            except Exception as quality_error:
                logger.info(f"Detection error ({quality_name}): {quality_error}")
                all_results[quality_name] = {"error": str(quality_error)}

        # Use the best quality buffer for alternative methods
        primary_buffer = img_buffers['quality_95']

        # Try with different detection methods and lower thresholds
        alternative_results = []

        # Method 1: Try with censor method if available (sometimes more sensitive)
        try:
            if hasattr(detector, 'censor'):
                logger.info("Trying censor method...")
                primary_buffer.seek(0)
                censor_result = detector.censor(primary_buffer.getvalue())
                logger.info(f"Censor result type: {type(censor_result)}")
                if censor_result:
                    alternative_results.append({"method": "censor", "result": str(censor_result)})
        except Exception as censor_error:
            logger.info(f"Censor method error: {censor_error}")

        # Method 2: Try to get raw predictions if available
        try:
            if hasattr(detector, 'predict'):
                logger.info("Trying predict method...")
                primary_buffer.seek(0)
                predict_result = detector.predict(primary_buffer.getvalue())
                logger.info(f"Predict result: {predict_result}")
                if predict_result:
                    alternative_results.append({"method": "predict", "result": predict_result})
        except Exception as predict_error:
            logger.info(f"Predict method error: {predict_error}")

        # Method 3: Try with PIL Image directly if supported
        try:
            # Some versions accept PIL Image
            pil_results = detector.detect(image)
            logger.info(f"PIL detection results: {pil_results}")
            if pil_results and pil_results != best_results:
                alternative_results.append({"method": "pil_direct", "result": pil_results})
                # Update best results if PIL gives better results
                if len(pil_results) > best_count:
                    best_results = pil_results
                    best_count = len(pil_results)
                    logger.info("Using PIL direct detection results")
        except Exception as pil_error:
            logger.info(f"PIL direct method error: {pil_error}")

        # Check available methods
        available_methods = []
        methods_to_check = ['detect', 'detect_video', 'censor', 'predict']
        for method in methods_to_check:
            if hasattr(detector, method):
                available_methods.append(method)

        return {
            "status": "success",
            "detections": best_results,
            "image_size": image.size,
            "detection_count": best_count,
            "all_detection_attempts": all_results,
            "alternative_methods": alternative_results,
            "available_methods": available_methods,
            "debug_info": {
                "original_mode": image.mode,
                "final_size": image.size,
                "has_detections": best_count > 0,
                "image_format": getattr(image, 'format', 'unknown'),
                "was_animated": hasattr(image, 'is_animated') and getattr(image, 'is_animated', False)
            }
        }
    except Exception as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.post("/detect")
async def detect_nudity(file: UploadFile = File(...)):
    """Detect nudity in uploaded image"""
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    # Read file
    contents = await file.read()
    
    # Process in thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, process_image, contents)
    
    return JSONResponse(content=result)

@app.get("/health")
async def health_check():
    return {"status": "healthy", "model_loaded": detector is not None}

@app.get("/model-info")
async def model_info():
    """Get information about the loaded model"""
    if detector is None:
        return {"error": "Model not loaded"}

    info = {
        "model_loaded": True,
        "detector_type": str(type(detector)),
        "available_methods": []
    }

    # Check available methods
    methods_to_check = ['detect', 'detect_video', 'censor', 'predict']
    for method in methods_to_check:
        if hasattr(detector, method):
            info["available_methods"].append(method)

    # Try to get model info if available
    try:
        if hasattr(detector, 'model'):
            info["has_model_attribute"] = True
        if hasattr(detector, 'model_path'):
            info["model_path"] = getattr(detector, 'model_path', 'unknown')
    except Exception as e:
        info["model_info_error"] = str(e)

    return info

@app.post("/detect-verbose")
async def detect_nudity_verbose(file: UploadFile = File(...)):
    """Detect nudity with verbose debugging information"""
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="File must be an image")

    # Read file
    contents = await file.read()

    # Process in thread pool to avoid blocking
    loop = asyncio.get_event_loop()
    result = await loop.run_in_executor(executor, process_image_verbose, contents)

    return JSONResponse(content=result)

def process_image_verbose(image_bytes: bytes) -> dict:
    """Verbose image processing with multiple detection attempts"""
    try:
        # Open and optimize image
        image = Image.open(io.BytesIO(image_bytes))
        logger.info(f"Original image size: {image.size}, mode: {image.mode}")

        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
            logger.info("Converted image to RGB")

        # Try multiple image sizes
        results_by_size = {}
        sizes_to_try = [
            ("original", image.size),
            ("large", (1024, 1024)),
            ("medium", (512, 512)),
            ("small", (256, 256))
        ]

        for size_name, target_size in sizes_to_try:
            try:
                # Resize image
                test_image = image.copy()
                if test_image.size != target_size:
                    test_image.thumbnail(target_size, Image.Resampling.LANCZOS)

                # Save to bytes
                img_buffer = io.BytesIO()
                test_image.save(img_buffer, format='JPEG', quality=95, optimize=True)
                img_buffer.seek(0)

                # Try detection
                results = detector.detect(img_buffer.getvalue())
                results_by_size[size_name] = {
                    "size": test_image.size,
                    "detections": results,
                    "count": len(results) if results else 0
                }
                logger.info(f"Size {size_name} ({test_image.size}): {len(results) if results else 0} detections")

            except Exception as size_error:
                results_by_size[size_name] = {"error": str(size_error)}
                logger.error(f"Error with size {size_name}: {size_error}")

        # Find best result
        best_result = []
        best_count = 0
        for size_name, result in results_by_size.items():
            if "detections" in result and result["count"] > best_count:
                best_result = result["detections"]
                best_count = result["count"]

        return {
            "status": "success",
            "best_detections": best_result,
            "best_count": best_count,
            "results_by_size": results_by_size,
            "image_info": {
                "original_size": image.size,
                "mode": image.mode
            }
        }

    except Exception as e:
        logger.error(f"Verbose processing error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Processing failed: {str(e)}")

@app.get("/")
async def root():
    return {"message": "NudeNet Detection API", "docs": "/docs", "endpoints": ["/detect", "/detect-verbose", "/model-info", "/health"]}