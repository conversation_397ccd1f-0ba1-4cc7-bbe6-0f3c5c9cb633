import nudenet
import sys

print(f"NudeNet version: {nudenet.__version__}")
print(f"Python version: {sys.version}")

# Check if we can import the detector
try:
    from nudenet import NudeDetector
    detector = NudeDetector()
    print(f"Detector type: {type(detector)}")
    print("Available attributes:")
    for attr in dir(detector):
        if not attr.startswith('_'):
            print(f"  - {attr}")
except Exception as e:
    print(f"Error: {e}")
