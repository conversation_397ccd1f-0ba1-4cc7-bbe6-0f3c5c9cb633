#!/usr/bin/env python3
"""
Aggressive testing for difficult images
"""

import sys
import os
from nudenet import NudeDetector
from PIL import Image, ImageEnhance
import io
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def aggressive_test(image_path):
    """Aggressive testing with image enhancements"""
    print(f"=== Aggressive Testing: {image_path} ===")
    
    if not os.path.exists(image_path):
        print(f"Image file not found: {image_path}")
        return
    
    try:
        # Initialize detector
        detector = NudeDetector()
        
        # Load image
        original_image = Image.open(image_path)
        print(f"Original: size={original_image.size}, mode={original_image.mode}, format={original_image.format}")
        
        # Test all frames if animated
        frames_to_test = []
        if hasattr(original_image, 'is_animated') and original_image.is_animated:
            print(f"Animated with {original_image.n_frames} frames - testing all frames")
            for i in range(original_image.n_frames):
                original_image.seek(i)
                frame = original_image.copy()
                frames_to_test.append((f"frame_{i}", frame))
        else:
            frames_to_test.append(("static", original_image))
        
        best_overall = []
        best_count = 0
        
        for frame_name, image in frames_to_test:
            print(f"\n--- Processing {frame_name} ---")
            
            # Convert to RGB
            if image.mode != 'RGB':
                if image.mode in ('RGBA', 'LA'):
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    if image.mode == 'RGBA':
                        background.paste(image, mask=image.split()[-1])
                    else:
                        background.paste(image)
                    image = background
                else:
                    image = image.convert('RGB')
            
            # Test different enhancements
            enhancements = [
                ("original", image),
                ("upscaled_2x", image.resize((image.size[0]*2, image.size[1]*2), Image.Resampling.LANCZOS)),
                ("upscaled_3x", image.resize((image.size[0]*3, image.size[1]*3), Image.Resampling.LANCZOS)),
                ("contrast_high", ImageEnhance.Contrast(image).enhance(1.5)),
                ("brightness_high", ImageEnhance.Brightness(image).enhance(1.2)),
                ("sharpness_high", ImageEnhance.Sharpness(image).enhance(1.5)),
            ]
            
            for enhancement_name, enhanced_image in enhancements:
                try:
                    print(f"  Testing {enhancement_name}: {enhanced_image.size}")
                    
                    # Test with high quality JPEG
                    buffer = io.BytesIO()
                    enhanced_image.save(buffer, format='JPEG', quality=100)
                    buffer.seek(0)
                    
                    results = detector.detect(buffer.getvalue())
                    count = len(results) if results else 0
                    
                    print(f"    {enhancement_name}: {count} detections")
                    if results:
                        for i, det in enumerate(results):
                            print(f"      {i+1}: {det}")
                    
                    if count > best_count:
                        best_overall = results
                        best_count = count
                        print(f"    *** NEW BEST: {count} detections from {frame_name} {enhancement_name} ***")
                        
                except Exception as e:
                    print(f"    Error with {enhancement_name}: {e}")
        
        print(f"\n=== FINAL RESULTS ===")
        print(f"Best detection count: {best_count}")
        if best_overall:
            for i, det in enumerate(best_overall):
                print(f"  {i+1}: {det}")
        else:
            print("No detections found with any method")
            print("\nPossible reasons:")
            print("1. Image content may not contain nudity")
            print("2. Image quality/resolution too low for detection")
            print("3. Model may not be sensitive enough for this type of content")
            print("4. Animation compression may have degraded important features")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        aggressive_test(sys.argv[1])
    else:
        print("Usage: python aggressive_test.py <image_path>")
        print("Example: python aggressive_test.py test_images/23793667.webp")
